<script src="{{ asset('assets/front-end/assets/js/core/popper.js') }}"></script>
<script src="{{ asset('assets/front-end/assets/js/core/bootstrap.min.js') }}" type="text/javascript"></script>
<script src="{{ asset('assets/front-end/assets/js/soft-design-system.js') }}" type="text/javascript"></script>
<script src="{{ asset('assets/front-end/assets/js/plugins/countup.min.js') }}" type="text/javascript"></script>
<script src="{{ asset('assets/front-end/assets/js/plugins/flatpickr.min.js') }}"></script>
<script src="{{ 'assets/front-end/assets/js/plugins/typedjs.js' }}"></script>
<script src="{{ asset('assets/front-end/assets/js/custom.js') }}"></script>
<script src="{{ asset('assets/js/tinymce.min.js') }}"></script>
<script src="{{ asset('assets/js/tinymce-jquery.min.js') }}"></script>

<!-- Date picker -->
<script src="{{ asset('assets/js/moment.min.js') }}"></script>

<script src="{{ asset('assets/js/daterangepicker.js') }}"></script>

<script src="{{ asset('assets/lightbox/lightbox.min.js') }}"></script>

<script src="{{ asset('assets/js/dropzone.min.js') }}"></script>
<script>
    var csrf_token = '{{ csrf_token() }}';
    var js_date_format = '{{ $js_date_format ?? 'YYYY-MM-DD' }}';
</script>


<script src="{{ asset('assets/front-end/assets/js/loopple/loopple.js') }}"></script>
<script src="{{ asset('assets/js/toastr.min.js') }}"></script>
<script src="{{ asset('assets/front-end/assets/js/plugins/lottie.js') }}"></script>

<script>
    var toastTimeOut = {{ isset($general_settings['toast_time_out']) ? $general_settings['toast_time_out'] : 5 }};
    var toastPosition = "{{ isset($general_settings['toast_position']) ? $general_settings['toast_position'] : 'toast-top-right' }}";
</script>
<script src="{{ asset('assets/js/location-tracker.js') }}"></script>
<script src="{{ asset('assets/js/custom.js') }}"></script>

@if (session()->has('message'))
<script>
    toastr.options = {
        "positionClass": toastPosition,
        "showDuration": "300",
        "hideDuration": "1000",
        "timeOut": parseFloat(toastTimeOut) * 1000,
        "progressBar": true,
        "extendedTimeOut": "1000",
        "closeButton": true
    };
    toastr.success('{{ session('message') }}', 'Success');
</script>
@elseif(session()->has('error'))
<script>
    toastr.options = {
        "positionClass": toastPosition,
        "showDuration": "300",
        "hideDuration": "1000",
        "timeOut": parseFloat(toastTimeOut) * 1000,
        "progressBar": true,
        "extendedTimeOut": "1000",
        "closeButton": true
    };
    toastr.error('{{ session('error') }}', 'Error');
</script>
@endif

<!-- ===== NESTKO CHATBOT WIDGET JAVASCRIPT ===== -->
<script>
// ===== LARAVEL CSRF TOKEN =====
const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';

// ===== NESTKO KNOWLEDGE BASE =====
const NESTKO_KNOWLEDGE = `
# NestKo Project Management Platform

## Overview
NestKo is your all-in-one solution for smarter project management. We simplify project and task management with powerful tools to organize tasks, collaborate in real-time, and track progress — all in one place. Our user-friendly interface makes it easy for anyone to get started, regardless of technical expertise.

## Company Mission
We are passionate about empowering teams to achieve peak productivity. NestKo makes project management simple and effective, helping teams collaborate, track tasks, and manage projects efficiently.

## Main Benefits
1. **Manage Projects with Ease** - Stay on top of deadlines, goals, and timelines with NestKo's smart project tools
2. **Simplify Task Management** - Assign, monitor, and complete tasks effectively with clear accountability
3. **Enhance Team Collaboration** - NestKo connects your team through messaging, meetings, and shared progress
4. **Powerful Reporting** - Make better decisions with real-time insights and custom reports

## Core Features - Every Feature Your Team Needs To Complete Work Faster

### Project Management
- Manage all your company projects from one dashboard
- Intuitive dashboards with customizable views
- Project templates and workflows
- Milestone tracking and deadline management
- Progress visualization and reporting
- Simple and intuitive project management with no learning curves
- Visualize projects with intuitive dashboards and customizable views

### Task Management & Organization
- Create and assign tasks to your team effectively
- Task priorities and deadlines
- Subtask creation and dependencies
- Flexible workspace system with custom statuses (To Do, In Progress, Completed)
- Task templates for recurring work
- Effective task organization with workspaces and statuses
- Break down complex projects into manageable tasks and subtasks
- Prioritize effectively by highlighting critical tasks

### Team Collaboration & Communication
- Instant messaging between team members
- Real-time team communication
- Comments, mentions, and discussions
- Activity feeds and notifications
- @mentions and notification system
- Foster seamless collaboration with built-in communication tools
- Stay on the same page with real-time task updates and activity feeds
- Centralize all project-related information, documents, and files in one accessible location
- Break down silos with improved team collaboration and communication

### User Management
- Handle team roles, permissions, and access easily
- User authentication and access control
- Role-based permissions
- Activity logging and audit trails

### Client Management
- Manage client records and communication
- Client project tracking
- Communication history

### Meetings & Virtual Collaboration
- Schedule and manage virtual meetings
- Virtual meeting scheduling and management
- Video conferencing integration
- Team announcements and updates

### Financial Management
- Track expenses and handle company finances
- Expense tracking and reporting
- Budget management and forecasting
- Invoice creation and management
- Financial dashboard and analytics
- Generate and distribute employee payslips
- Payroll management and employee payment tracking

### Productivity & Efficiency Features
- Automate repetitive tasks to free up valuable time
- Minimize distractions with centralized task management
- Built-in time tracking and milestone management
- Progress reporting and deadline management
- Meet deadlines with confidence

### Language & Accessibility
- Use the app in multiple languages
- Multi-language support for global teams

### Security & Compliance
- Enterprise-grade security features
- Data encryption and secure access
- Regular security updates and monitoring
- Compliance with industry standards

## Key Features in Detail

### Streamline Your Projects
Take control of your projects and boost team productivity with NestKo, the all-in-one project management and task management solution. Our cloud-based platform empowers you to effortlessly organize projects, collaborate with your team, and track progress – all in one place.

### Effortless Organization
NestKo provides a centralized hub to create, manage, and track all your projects. Say goodbye to scattered tasks and missed deadlines – our intuitive interface keeps everything organized and accessible.

### Seamless Collaboration
Foster a collaborative work environment with NestKo. Assign tasks, share files, and communicate effectively with your team in real-time. Ensure everyone is on the same page and working towards a common goal.

### Visualize Project Health
Get insightful dashboards and reports to monitor project performance and identify areas for improvement.

## About Us - Simplifying Project & Task Management
NestKo is dedicated to simplifying project and task management for teams worldwide. We believe in:

1. **Managing Projects Efficiently** - Simplify your workflow with powerful tools to organize tasks, collaborate in real-time, and track progress — all in one place.

2. **Assign and Monitor Tasks** - Easily assign tasks to team members, set priorities, and track progress in real-time to ensure nothing falls through the cracks.

3. **Enhance Collaboration** - Connect your team with real-time communication, shared tasks, and seamless file sharing to boost teamwork and productivity.

## Frequently Asked Questions

### What are the key features of a project management system?
Key features typically include task management, team collaboration, project planning and scheduling, time tracking, file sharing, reporting and analytics, and integration with other tools.

### Can I handle multiple projects simultaneously?
Yes, most project management systems are designed to support the management of multiple projects concurrently. They typically provide features for organizing projects into separate workspaces or folders, allowing teams to easily switch between projects.

### How do I choose the right project management system?
When selecting a project management system, consider factors such as your team's size and requirements, the complexity of your projects, ease of use, scalability, customization options, pricing, customer support, and compatibility with existing tools and workflows. It's also helpful to try out different systems through free trials or demos to evaluate their suitability for your needs.

## Pricing Plans

### Free Plan
- Up to 5 users
- 3 projects
- Basic task management
- 1GB storage
- Email support

### Professional Plan ($9.99/month per user)
- Unlimited users
- Unlimited projects
- Advanced task management
- 100GB storage
- Priority support
- Time tracking
- Custom fields
- Advanced reporting

### Enterprise Plan ($19.99/month per user)
- Everything in Professional
- Advanced security features
- Custom integrations
- Dedicated account manager
- SLA guarantee
- Advanced analytics
- White-label options

## Getting Started
1. Sign up for a free account or start your free trial
2. Create your first project
3. Invite team members
4. Start organizing tasks and collaborating
5. Track progress and meet deadlines

## Contact Us & Support
Have questions or need support? Reach out to us!
- 24/7 customer support available
- Comprehensive documentation and tutorials
- Community forums for user discussions
- Regular webinars and training sessions
- Email support for technical assistance
- Contact form available on our website
- Live chat support

## Integration Capabilities
- Slack integration for seamless communication
- Google Workspace integration
- Microsoft Office 365 compatibility
- Zapier connections for workflow automation
- API access for custom integrations
- Popular tools and services integration

## Mobile & Accessibility
- iOS app available on App Store
- Android app available on Google Play
- Mobile-responsive design for work on any device
- Offline capability for mobile apps
- Cross-platform compatibility

## Why Choose NestKo?
- User-friendly interface with minimal learning curve
- Comprehensive feature set for complete project management
- Scalable solution that grows with your team
- Regular updates and new feature releases
- Strong focus on team collaboration and productivity
- Reliable cloud-based platform with 99.9% uptime
- Integration with popular tools and services
- No more learning curves - easy for anyone to get started
- Visualize projects with intuitive dashboards and customizable views
- Competitive pricing with flexible plans
- Proven track record with thousands of satisfied customers
`;

// ===== OPENROUTER AI INTEGRATION =====
const OPENROUTER_CONFIG = {
    API_KEY: '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',
    BASE_URL: 'https://openrouter.ai/api/v1',
    MODEL: 'z-ai/glm-4.5-air:free'
};

// ===== WIDGET STATE VARIABLES =====
let isWidgetOpen = false;
let currentScreen = 'welcome';
let messages = [];
let sessionId = '';
let isTyping = false;
let conversationHistory = [];

// ===== EMOJI DATA =====
const emojis = [
    "😀", "😃", "😄", "😁", "😆", "😅", "😂", "🤣", "😊", "😇", "🙂", "🙃", "😉", "😌", "😍", "🥰",
    "😘", "😗", "😙", "😚", "😋", "😛", "😝", "😜", "🤪", "🤨", "🧐", "🤓", "😎", "🤩", "🥳", "😏",
    "👋", "🤚", "🖐️", "✋", "🖖", "👌", "🤏", "✌️", "🤞", "🤟", "🤘", "🤙", "👈", "👉", "👆", "👇",
    "☝️", "👍", "👎", "👊", "✊", "🤛", "🤜", "👏", "🙌", "👐", "🤲", "🤝", "🙏", "❤️", "🧡", "💛",
    "💚", "💙", "💜", "🖤", "🤍", "🤎", "💔", "❣️", "💕", "💞", "💓", "💗", "💖", "💘", "💝", "💟"
];

// ===== FAQ QUICK ACTIONS =====
const nestkoFAQs = [
    { text: "💼 Project Management", query: "How does NestKo help with project management?" },
    { text: "💰 Pricing Plans", query: "What are NestKo's pricing plans and features?" },
    { text: "🚀 Getting Started", query: "How do I get started with NestKo?" },
    { text: "👥 Team Collaboration", query: "How does team collaboration work in NestKo?" },
    { text: "📊 Reports & Analytics", query: "What reporting and analytics features does NestKo offer?" },
    { text: "Just browsing 👀", query: "Just browsing, show me what NestKo can do" }
];

// ===== WIDGET CONTROL FUNCTIONS =====
function toggleWidget() {
    if (isWidgetOpen) {
        minimizeWidget();
    } else {
        openWidget();
    }
}

function openWidget() {
    const trigger = document.getElementById('widget-trigger');
    const main = document.getElementById('widget-main');

    // Smooth transition
    trigger.style.opacity = '0';
    trigger.style.transform = 'scale(0.8)';

    setTimeout(() => {
        trigger.style.display = 'none';
        main.style.display = 'flex';
        main.style.opacity = '0';
        main.style.transform = 'scale(0.9) translateY(20px)';

        // Animate in
        requestAnimationFrame(() => {
            main.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
            main.style.opacity = '1';
            main.style.transform = 'scale(1) translateY(0)';
        });
    }, 200);

    isWidgetOpen = true;

    if (!sessionId) {
        createSession();
    }
}

function minimizeWidget() {
    const trigger = document.getElementById('widget-trigger');
    const main = document.getElementById('widget-main');

    // Smooth transition out
    main.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
    main.style.opacity = '0';
    main.style.transform = 'scale(0.9) translateY(20px)';

    setTimeout(() => {
        main.style.display = 'none';
        trigger.style.display = 'block';
        trigger.style.opacity = '0';
        trigger.style.transform = 'scale(0.8)';

        // Animate trigger back in
        requestAnimationFrame(() => {
            trigger.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            trigger.style.opacity = '1';
            trigger.style.transform = 'scale(1)';
        });
    }, 300);

    isWidgetOpen = false;
}

function closeWidget() {
    minimizeWidget();
    currentScreen = 'welcome';
    showWelcomeScreen();
}

function startChat() {
    currentScreen = 'chat';
    showChatScreen();

    addMessage('assistant', "Hello there! 👋 It's nice to meet you!");
    addMessage('assistant', "What brings you here today? Please use the navigation below or ask me anything about NestKo's project management features! 🚀");

    showFAQButtons();
}

function startChatWithAnimation() {
    // Add button click animation
    const chatBtn = event.target;
    if (chatBtn.tagName === 'BUTTON') {
        chatBtn.style.transform = 'scale(0.95)';
        chatBtn.style.transition = 'all 0.15s ease';

        setTimeout(() => {
            chatBtn.style.transform = 'scale(1)';
        }, 150);
    }

    // Add slide animation to welcome screen
    const welcomeScreen = document.getElementById('welcome-screen');
    const chatScreen = document.getElementById('chat-screen');

    // Slide out welcome screen
    welcomeScreen.style.transition = 'transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.6s ease';
    welcomeScreen.style.transform = 'translateX(-100%)';
    welcomeScreen.style.opacity = '0';

    setTimeout(() => {
        // Hide welcome screen and show chat screen
        welcomeScreen.style.display = 'none';
        chatScreen.style.display = 'flex';
        chatScreen.style.transform = 'translateX(100%)';
        chatScreen.style.opacity = '0';

        // Animate chat screen in
        requestAnimationFrame(() => {
            chatScreen.style.transition = 'transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 0.6s ease';
            chatScreen.style.transform = 'translateX(0)';
            chatScreen.style.opacity = '1';
        });

        // Initialize chat
        currentScreen = 'chat';
    }, 300);
}

function newChat() {
    messages = [];
    conversationHistory = [];
    clearMessages();
    startChat();
}

function showWelcomeScreen() {
    const welcomeScreen = document.getElementById('welcome-screen');
    const chatScreen = document.getElementById('chat-screen');

    // Reset any transforms and show welcome screen
    welcomeScreen.style.display = 'flex';
    welcomeScreen.style.transform = 'translateX(0)';
    welcomeScreen.style.opacity = '1';
    welcomeScreen.style.transition = 'none';

    chatScreen.style.display = 'none';
}

function showChatScreen() {
    const welcomeScreen = document.getElementById('welcome-screen');
    const chatScreen = document.getElementById('chat-screen');

    // Reset any transforms and show chat screen
    welcomeScreen.style.display = 'none';
    chatScreen.style.display = 'flex';
    chatScreen.style.transform = 'translateX(0)';
    chatScreen.style.opacity = '1';
    chatScreen.style.transition = 'none';
}

// ===== SESSION MANAGEMENT =====
function createSession() {
    sessionId = 'laravel-session-' + Math.random().toString(36).substr(2, 9) + '-' + Date.now();
}

// ===== MESSAGE HANDLING =====
function addMessage(role, content, confidence = null, suggestions = null) {
    const message = {
        id: 'msg-' + Date.now() + '-' + Math.random().toString(36).substr(2, 5),
        role: role,
        content: content,
        timestamp: new Date(),
        confidence: confidence,
        suggestions: suggestions
    };

    messages.push(message);
    renderMessage(message);
    scrollToBottom();

    conversationHistory.push({
        role: role,
        content: content
    });

    if (conversationHistory.length > 10) {
        conversationHistory = conversationHistory.slice(-10);
    }
}

function showWelcomeScreen() {
    const welcomeScreen = document.getElementById('welcome-screen');
    const chatScreen = document.getElementById('chat-screen');

    // Slide out chat screen
    chatScreen.style.transform = 'translateX(100%)';
    chatScreen.style.opacity = '0';

    setTimeout(() => {
        chatScreen.style.display = 'none';
        welcomeScreen.style.display = 'flex';
        welcomeScreen.style.transform = 'translateX(-100%)';
        welcomeScreen.style.opacity = '0';

        // Animate welcome screen in
        setTimeout(() => {
            welcomeScreen.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
            welcomeScreen.style.transform = 'translateX(0)';
            welcomeScreen.style.opacity = '1';
        }, 50);
    }, 200);

    currentScreen = 'welcome';
}

function showHomeView() {
    // This function handles the home navigation
    const navItems = document.querySelectorAll('.nav-item-modern');
    navItems.forEach(item => item.classList.remove('active'));
    event.target.closest('.nav-item-modern').classList.add('active');
}

function handleQuickMessage(message) {
    // Add user message to chat
    addMessageToChat('user', message);

    // Show AI typing animation
    showAITyping();

    // Send to AI API
    sendToAI(message);
}

function sendToAI(message) {
    const apiKey = '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';

    fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': window.location.origin,
            'X-Title': 'NestKo Assistant'
        },
        body: JSON.stringify({
            model: 'z-ai/glm-4.5-air:free',
            messages: [
                {
                    role: 'system',
                    content: 'You are NestKo AI Assistant, a helpful and friendly AI assistant for the NestKo project management platform. Keep responses concise and helpful. Always maintain a professional yet approachable tone. Focus on project management, team collaboration, and productivity solutions.'
                },
                {
                    role: 'user',
                    content: message
                }
            ],
            temperature: 0.7,
            max_tokens: 500
        })
    })
    .then(response => response.json())
    .then(data => {
        hideAITyping();

        if (data.choices && data.choices[0] && data.choices[0].message) {
            const aiResponse = data.choices[0].message.content;
            addMessageToChat('assistant', aiResponse);
        } else {
            addMessageToChat('assistant', 'I apologize, but I encountered an error. Please try again or contact our support team.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        hideAITyping();
        addMessageToChat('assistant', 'I apologize, but I encountered a connection error. Please check your internet connection and try again.');
    });
}

function addMessageToChat(role, content) {
    const messagesContainer = document.getElementById('messages-container');

    if (role === 'user') {
        const userMessage = document.createElement('div');
        userMessage.className = 'user-message-ref';
        userMessage.style.cssText = `
            background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%);
            color: white;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.5;
            max-width: 280px;
            margin-left: auto;
            margin-right: 16px;
            margin-bottom: 16px;
            align-self: flex-end;
            box-shadow: 0 2px 8px rgba(211, 47, 47, 0.3);
            animation: messageSlideIn 0.3s ease-out;
        `;
        userMessage.textContent = content;
        messagesContainer.appendChild(userMessage);
    } else {
        const messageWrapper = document.createElement('div');
        messageWrapper.className = 'message-with-avatar-ref';
        messageWrapper.innerHTML = `
            <div class="message-avatar-ref">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <circle cx="12" cy="12" r="10" fill="#D32F2F"/>
                    <path d="M8 14s1.5 2 4 2 4-2 4-2M9 9h.01M15 9h.01" stroke="white" stroke-width="2" stroke-linecap="round"/>
                </svg>
            </div>
            <div class="message-content-ref">
                <div class="message-text-ref" style="animation: messageSlideIn 0.3s ease-out;">
                    ${escapeHtml(content)}
                </div>
            </div>
        `;
        messagesContainer.appendChild(messageWrapper);
    }

    // Scroll to bottom
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function sendMessage() {
    const input = document.getElementById('message-input');
    const message = input.value.trim();

    if (message) {
        addMessageToChat('user', message);
        input.value = '';

        // Show AI typing and send to API
        showAITyping();
        sendToAI(message);
    }
}

function handleKeyPress(event) {
    if (event.key === 'Enter') {
        sendMessage();
    }
}

// AI Animation Functions
function showAITyping() {
    const headerAvatar = document.getElementById('ai-avatar-header');
    if (headerAvatar) {
        headerAvatar.classList.add('typing');
    }
}

function hideAITyping() {
    const headerAvatar = document.getElementById('ai-avatar-header');
    if (headerAvatar) {
        headerAvatar.classList.remove('typing');
    }
}

// Enhanced Emoji Picker
function toggleEmojiPicker() {
    const emojiPicker = document.getElementById('emoji-picker');
    const emojiGrid = document.getElementById('emoji-grid');

    if (emojiPicker.style.display === 'none' || !emojiPicker.style.display) {
        // Populate emojis if not already done
        if (!emojiGrid.hasChildNodes()) {
            populateEmojis();
        }
        emojiPicker.style.display = 'block';
    } else {
        emojiPicker.style.display = 'none';
    }
}

function populateEmojis() {
    const emojis = [
        '😊', '😂', '❤️', '👍', '🎉', '👋',
        '🤔', '💡', '🔥', '✨', '🚀', '💯',
        '😍', '🥳', '😎', '🤗', '😇', '🙌',
        '💪', '👏', '🎯', '⭐', '🌟', '💫'
    ];

    const emojiGrid = document.getElementById('emoji-grid');
    emojiGrid.innerHTML = '';

    emojis.forEach(emoji => {
        const emojiItem = document.createElement('div');
        emojiItem.className = 'emoji-item-ref';
        emojiItem.textContent = emoji;
        emojiItem.onclick = () => {
            const input = document.getElementById('message-input');
            input.value += emoji;
            document.getElementById('emoji-picker').style.display = 'none';
            input.focus();
        };
        emojiGrid.appendChild(emojiItem);
    });
}

// AI Rephrase Function
function rephraseWithAI() {
    const input = document.getElementById('message-input');
    const message = input.value.trim();
    const rephraseBtn = document.getElementById('ai-rephrase-btn');

    if (!message) {
        alert('Please enter a message to rephrase');
        return;
    }

    // Show processing animation
    rephraseBtn.classList.add('processing');

    const apiKey = '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';

    fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': window.location.origin,
            'X-Title': 'NestKo Assistant'
        },
        body: JSON.stringify({
            model: 'z-ai/glm-4.5-air:free',
            messages: [
                {
                    role: 'system',
                    content: 'You are a helpful assistant that rephrases text to make it more professional, clear, and polite. Only return the rephrased text, nothing else.'
                },
                {
                    role: 'user',
                    content: `Please rephrase this message to make it more professional and clear: "${message}"`
                }
            ],
            temperature: 0.7,
            max_tokens: 200
        })
    })
    .then(response => response.json())
    .then(data => {
        rephraseBtn.classList.remove('processing');

        if (data.choices && data.choices[0] && data.choices[0].message) {
            const rephrasedText = data.choices[0].message.content.trim();
            input.value = rephrasedText;
        } else {
            alert('Failed to rephrase. Please try again.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        rephraseBtn.classList.remove('processing');
        alert('Failed to rephrase. Please check your connection.');
    });
}

// Three Dots Menu Functions
function toggleThreeDotsMenu() {
    let menu = document.getElementById('three-dots-menu');

    if (!menu) {
        // Create the menu if it doesn't exist
        menu = document.createElement('div');
        menu.id = 'three-dots-menu';
        menu.className = 'three-dots-menu';
        menu.innerHTML = `
            <div class="menu-item" onclick="downloadChatAsPDF()">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                    <polyline points="7,10 12,15 17,10"/>
                    <line x1="12" y1="15" x2="12" y2="3"/>
                </svg>
                Download Chat
            </div>
        `;

        // Position the menu
        const threeDotsBtn = document.querySelector('.three-dots-btn');
        const rect = threeDotsBtn.getBoundingClientRect();
        menu.style.position = 'absolute';
        menu.style.top = (rect.bottom + 5) + 'px';
        menu.style.right = '16px';
        menu.style.zIndex = '1001';

        document.body.appendChild(menu);
    }

    // Toggle visibility
    if (menu.style.display === 'none' || !menu.style.display) {
        menu.style.display = 'block';
    } else {
        menu.style.display = 'none';
    }
}

// Download Chat as PDF Function
function downloadChatAsPDF() {
    const messagesContainer = document.getElementById('messages-container');
    const messages = messagesContainer.querySelectorAll('.message-with-avatar-ref, .user-message-ref');

    if (messages.length === 0) {
        alert('No messages to download');
        return;
    }

    // Create PDF content
    let pdfContent = `
        <html>
        <head>
            <title>NestKo AI Chat Export</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #D32F2F; padding-bottom: 10px; }
                .message { margin-bottom: 15px; padding: 10px; border-radius: 8px; }
                .user-message { background: #FFEBEE; margin-left: 50px; }
                .ai-message { background: #F5F5F5; margin-right: 50px; }
                .timestamp { font-size: 12px; color: #666; margin-top: 5px; }
                .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; border-top: 1px solid #ccc; padding-top: 10px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1 style="color: #D32F2F;">NestKo AI Chat Export</h1>
                <p>Generated on ${new Date().toLocaleString()}</p>
            </div>
    `;

    // Extract messages
    messages.forEach((message, index) => {
        const isUser = message.classList.contains('user-message-ref');
        const messageText = isUser ?
            message.textContent :
            message.querySelector('.message-text-ref')?.textContent || '';

        pdfContent += `
            <div class="message ${isUser ? 'user-message' : 'ai-message'}">
                <strong>${isUser ? 'You' : 'NestKo AI'}:</strong><br>
                ${messageText}
                <div class="timestamp">Message ${index + 1}</div>
            </div>
        `;
    });

    pdfContent += `
            <div class="footer">
                <p>Powered by NestKo AI - Your Project Management Assistant</p>
                <p>Visit us at ${window.location.origin}</p>
            </div>
        </body>
        </html>
    `;

    // Create and download PDF
    const printWindow = window.open('', '_blank');
    printWindow.document.write(pdfContent);
    printWindow.document.close();

    // Wait for content to load then print
    setTimeout(() => {
        printWindow.print();
        printWindow.close();
    }, 500);

    // Hide the menu
    document.getElementById('three-dots-menu').style.display = 'none';
}

// Close menus when clicking outside
document.addEventListener('click', function(event) {
    const emojiPicker = document.getElementById('emoji-picker');
    const emojiBtn = document.querySelector('.emoji-btn-ref');
    const threeDotsMenu = document.getElementById('three-dots-menu');
    const threeDotsBtn = document.querySelector('.three-dots-btn');

    // Close emoji picker
    if (emojiPicker && !emojiPicker.contains(event.target) && !emojiBtn.contains(event.target)) {
        emojiPicker.style.display = 'none';
    }

    // Close three dots menu
    if (threeDotsMenu && !threeDotsMenu.contains(event.target) && !threeDotsBtn.contains(event.target)) {
        threeDotsMenu.style.display = 'none';
    }
});

function clearMessages() {
    document.getElementById('messages-container').innerHTML = '';
}

function scrollToBottom() {
    const container = document.getElementById('messages-container');
    container.scrollTop = container.scrollHeight;
}

function showFAQButtons() {
    const messagesContainer = document.getElementById('messages-container');
    const faqElement = document.createElement('div');
    faqElement.className = 'flex flex-wrap gap-2 ml-11';
    faqElement.style.cssText = 'display: flex; flex-wrap: wrap; gap: 0.5rem; margin-left: 2.75rem;';

    let faqHTML = '';
    nestkoFAQs.forEach(faq => {
        faqHTML += `<button onclick="handleQuickMessage('${faq.query}')" class="quick-action-btn">${faq.text}</button>`;
    });

    faqElement.innerHTML = faqHTML;
    messagesContainer.appendChild(faqElement);
    scrollToBottom();
}



// ===== INPUT HANDLING =====
function handleKeyPress(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
}



// ===== AI INTEGRATION =====


// ===== EMOJI FUNCTIONALITY =====
function toggleEmojiPicker() {
    const picker = document.getElementById('emoji-picker');
    const isVisible = picker.style.display !== 'none';

    if (isVisible) {
        picker.style.display = 'none';
    } else {
        populateEmojiPicker();
        picker.style.display = 'block';
    }
}

function populateEmojiPicker() {
    const grid = document.getElementById('emoji-grid');
    if (grid.children.length === 0) {
        emojis.forEach(emoji => {
            const button = document.createElement('button');
            button.textContent = emoji;
            button.className = 'p-2 hover:bg-gray-100 rounded text-lg cursor-pointer';
            button.style.cssText = 'padding: 0.5rem; border-radius: 0.25rem; font-size: 1.125rem; cursor: pointer; background: none; border: none; transition: background-color 0.2s;';
            button.onclick = () => insertEmoji(emoji);
            grid.appendChild(button);
        });
    }
}

function insertEmoji(emoji) {
    const input = document.getElementById('message-input');
    input.value += emoji;
    input.focus();
    document.getElementById('emoji-picker').style.display = 'none';
}

// ===== ADDITIONAL FEATURES =====
function rephraseMessage() {
    const input = document.getElementById('message-input');
    const currentText = input.value.trim();

    if (currentText) {
        // Simple rephrase suggestions
        const rephrasedOptions = [
            `Could you help me understand ${currentText.toLowerCase()}?`,
            `I'd like to learn more about ${currentText.toLowerCase()}`,
            `Can you explain ${currentText.toLowerCase()} in detail?`,
            `What can you tell me about ${currentText.toLowerCase()}?`
        ];

        const randomRephrase = rephrasedOptions[Math.floor(Math.random() * rephrasedOptions.length)];
        input.value = randomRephrase;
        input.focus();
    }
}

function showSignupForm() {
    addMessage('assistant', "Great! I'd love to help you get started with NestKo. You can sign up for our free plan right now - no credit card required! 🎉");
    addMessage('assistant', "Our free plan includes up to 5 users, 3 projects, and 1GB storage. Perfect for small teams to get started. Would you like me to guide you through the signup process or answer any questions first?");

    // You can add actual signup logic here
    setTimeout(() => {
        addMessage('assistant', "You can sign up directly on our homepage, or I can connect you with our sales team for a personalized demo. What would you prefer?");
    }, 2000);
}

function showContactForm() {
    addMessage('assistant', "I'm here to help! You can reach our support team in several ways:");
    addMessage('assistant', "📧 Email: <EMAIL>\n📞 Phone: 1-800-NESTKO\n💬 Live Chat: Available 24/7\n📚 Help Center: docs.nestko.com");
    addMessage('assistant', "Is there something specific I can help you with right now, or would you prefer to contact our support team directly?");
}

// ===== UTILITY FUNCTIONS =====
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function cleanAIResponse(text) {
    // Remove markdown symbols but keep emojis and basic formatting
    let cleaned = text
        // Remove markdown headers (# ## ###)
        .replace(/^#{1,6}\s+/gm, '')
        // Remove bold/italic markers (* ** _)
        .replace(/\*\*([^*]+)\*\*/g, '$1')
        .replace(/\*([^*]+)\*/g, '$1')
        .replace(/_([^_]+)_/g, '$1')
        // Remove markdown list markers (- * +)
        .replace(/^[\s]*[-\*\+]\s+/gm, '• ')
        // Remove extra whitespace but preserve line breaks
        .replace(/\n\s*\n\s*\n/g, '\n\n')
        .trim();

    return cleaned;
}

// ===== ENHANCED ANIMATIONS =====
function addBlinkingAnimation() {
    const eyes = document.querySelectorAll('.chatbot-eye');
    let blinkCount = 0;

    function blink() {
        if (blinkCount < 3) {
            eyes.forEach(eye => {
                eye.style.transition = 'transform 0.15s ease-in-out';
                eye.style.transform = 'scaleY(0.2)';
                setTimeout(() => {
                    eye.style.transform = 'scaleY(1)';
                }, 150);
            });
            blinkCount++;
            setTimeout(blink, 1000);
        }
    }

    // Start blinking after a short delay
    setTimeout(blink, 1500);
}

function addHoverBlinkEffect() {
    const chatbotFace = document.querySelector('.chatbot-face');
    if (chatbotFace) {
        chatbotFace.addEventListener('mouseenter', function() {
            const eyes = document.querySelectorAll('.chatbot-eye');
            eyes.forEach(eye => {
                eye.style.transition = 'transform 0.2s ease-in-out';
                eye.style.transform = 'scaleY(0.3)';
                setTimeout(() => {
                    eye.style.transform = 'scaleY(1)';
                }, 200);
            });
        });
    }
}

function enhanceWidgetAnimations() {
    const widgetMain = document.getElementById('widget-main');
    const widgetTrigger = document.getElementById('widget-trigger');

    // Add smooth transitions
    if (widgetMain) {
        widgetMain.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
    }

    if (widgetTrigger) {
        widgetTrigger.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
    }
}

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
    // Initialize emoji picker
    populateEmojiPicker();

    // Add blinking animation
    addBlinkingAnimation();

    // Add hover blink effect
    addHoverBlinkEffect();

    // Enhance animations
    enhanceWidgetAnimations();

    // Close emoji picker when clicking outside
    document.addEventListener('click', function(event) {
        const picker = document.getElementById('emoji-picker');
        const emojiButton = event.target.closest('[onclick="toggleEmojiPicker()"]');

        if (!picker.contains(event.target) && !emojiButton) {
            picker.style.display = 'none';
        }
    });

    // Auto-focus message input when chat screen is shown
    const messageInput = document.getElementById('message-input');
    if (messageInput) {
        messageInput.addEventListener('focus', function() {
            document.getElementById('emoji-picker').style.display = 'none';
        });

        // Add enhanced input animations
        messageInput.addEventListener('focus', function() {
            this.parentElement.style.transform = 'translateY(-2px)';
        });

        messageInput.addEventListener('blur', function() {
            this.parentElement.style.transform = 'translateY(0)';
        });
    }

    // Add hover effects to chat bubbles
    document.addEventListener('mouseover', function(event) {
        if (event.target.closest('.chat-bubble-user') || event.target.closest('.chat-bubble-assistant')) {
            event.target.closest('.chat-bubble-user, .chat-bubble-assistant').style.transform = 'translateY(-2px)';
        }
    });

    document.addEventListener('mouseout', function(event) {
        if (event.target.closest('.chat-bubble-user') || event.target.closest('.chat-bubble-assistant')) {
            event.target.closest('.chat-bubble-user, .chat-bubble-assistant').style.transform = 'translateY(0)';
        }
    });
});
</script>

