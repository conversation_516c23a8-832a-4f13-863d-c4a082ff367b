<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('task_locations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('task_id')->constrained()->onDelete('cascade');
            
            // Basic Location Data
            $table->decimal('latitude', 10, 7);
            $table->decimal('longitude', 10, 7);
            $table->string('action'); // 'task_created', 'checkin', 'checkout', etc.
            
            // Enhanced Location Data
            $table->decimal('accuracy', 8, 2)->nullable(); // GPS accuracy in meters
            $table->decimal('altitude', 8, 2)->nullable(); // Altitude in meters
            $table->decimal('speed', 8, 2)->nullable(); // Speed in m/s
            $table->decimal('heading', 5, 2)->nullable(); // Compass heading in degrees
            
            // Address Information (Reverse Geocoding)
            $table->text('address')->nullable();
            $table->string('city')->nullable();
            $table->string('country')->nullable();
            $table->string('postal_code')->nullable();
            
            // Tracking Context
            $table->string('tracking_type')->default('manual'); // 'manual', 'automatic', 'geofence', etc.
            $table->string('location_source')->default('gps'); // 'gps', 'network', 'passive', etc.
            
            // Device Context
            $table->integer('battery_level')->nullable(); // Battery percentage
            $table->string('device_type')->nullable(); // 'mobile', 'tablet', 'desktop'
            $table->string('network_type')->nullable(); // 'wifi', 'cellular', etc.
            $table->json('device_info')->nullable(); // Additional device information
            
            // Analytics Data
            $table->decimal('distance_from_previous', 10, 2)->nullable(); // Distance from previous location in meters
            $table->integer('duration_since_previous')->nullable(); // Time since previous location in seconds
            $table->boolean('is_significant_location')->default(false); // Whether this is a significant location change
            
            // Geofencing
            $table->json('geofence_zones')->nullable(); // Array of geofence zone IDs
            $table->string('geofence_event')->nullable(); // 'entry', 'exit', 'dwell'
            
            // Privacy and Security
            $table->boolean('is_encrypted')->default(false);
            $table->timestamp('expires_at')->nullable(); // For automatic data cleanup
            
            $table->timestamps();
            
            // Regular Indexes for Performance
            $table->index(['user_id', 'created_at'], 'user_date_index');
            $table->index(['task_id', 'action'], 'task_action_index');
            $table->index(['action', 'created_at'], 'action_date_index');
            $table->index('is_significant_location', 'significant_location_index');
            $table->index(['latitude', 'longitude'], 'lat_lng_index');
        });

        // Add spatial index using raw SQL (more compatible)
        try {
            DB::statement('ALTER TABLE task_locations ADD INDEX location_spatial_index (latitude, longitude)');
        } catch (\Exception $e) {
            // If spatial index fails, continue without it
            \Log::warning('Could not create spatial index for task_locations: ' . $e->getMessage());
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('task_locations');
    }
};