<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('geofence_zones', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->foreignId('workspace_id')->constrained()->onDelete('cascade');
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            
            // Zone Configuration
            $table->string('zone_type')->default('circle'); // 'circle', 'polygon', 'rectangle'
            $table->decimal('center_latitude', 10, 7);
            $table->decimal('center_longitude', 10, 7);
            $table->decimal('radius', 8, 2)->nullable(); // For circle zones (in meters)
            $table->json('polygon_coordinates')->nullable(); // For polygon zones
            
            // Zone Settings
            $table->boolean('is_active')->default(true);
            $table->string('zone_purpose')->default('general'); // 'office', 'client_site', 'restricted', etc.
            $table->string('alert_type')->default('both'); // 'entry', 'exit', 'both', 'none', 'dwell'
            $table->integer('dwell_time_threshold')->nullable(); // Minutes for dwell alerts
            
            // Notification Settings
            $table->boolean('notify_managers')->default(false);
            $table->boolean('notify_admins')->default(false);
            $table->boolean('notify_user')->default(false);
            $table->json('notification_settings')->nullable();
            
            // Time-based Activation
            $table->json('active_hours')->nullable(); // {'start': '09:00', 'end': '17:00'}
            $table->json('active_days')->nullable(); // [1,2,3,4,5] for Mon-Fri
            $table->boolean('respect_working_hours')->default(false);
            
            // Analytics
            $table->integer('entry_count')->default(0);
            $table->integer('exit_count')->default(0);
            $table->timestamp('last_activity')->nullable();
            
            $table->timestamps();
            
            // Regular Indexes
            $table->index(['workspace_id', 'is_active'], 'workspace_active_index');
            $table->index(['zone_type', 'is_active'], 'type_active_index');
            $table->index(['center_latitude', 'center_longitude'], 'center_location_index');
        });

        // Add spatial index using raw SQL (more compatible)
        try {
            DB::statement('ALTER TABLE geofence_zones ADD INDEX center_spatial_index (center_latitude, center_longitude)');
        } catch (\Exception $e) {
            // If spatial index fails, continue without it
            \Log::warning('Could not create spatial index for geofence_zones: ' . $e->getMessage());
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('geofence_zones');
    }
};