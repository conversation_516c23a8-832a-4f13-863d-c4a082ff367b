"use strict";

/**
 * Location Tracker Module
 * Handles GPS coordinate capture for task operations
 */
class LocationTracker {
    constructor() {
        this.isSupported = 'geolocation' in navigator;
        this.options = {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 60000
        };
        this.csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 
                        document.querySelector('input[name="_token"]')?.value;
    }

    /**
     * Check if geolocation is supported
     */
    isGeolocationSupported() {
        return this.isSupported;
    }

    /**
     * Get current position with promise
     */
    getCurrentPosition() {
        return new Promise((resolve, reject) => {
            if (!this.isSupported) {
                reject(new Error('Geolocation is not supported by this browser.'));
                return;
            }

            navigator.geolocation.getCurrentPosition(
                (position) => {
                    resolve({
                        latitude: position.coords.latitude,
                        longitude: position.coords.longitude,
                        accuracy: position.coords.accuracy,
                        timestamp: position.timestamp
                    });
                },
                (error) => {
                    let errorMessage = 'Unknown location error';
                    switch (error.code) {
                        case error.PERMISSION_DENIED:
                            errorMessage = 'Location access denied by user.';
                            break;
                        case error.POSITION_UNAVAILABLE:
                            errorMessage = 'Location information is unavailable.';
                            break;
                        case error.TIMEOUT:
                            errorMessage = 'Location request timed out.';
                            break;
                    }
                    reject(new Error(errorMessage));
                },
                this.options
            );
        });
    }

    /**
     * Capture and send location for a task action
     */
    async captureLocation(taskId, action, options = {}) {
        try {
            // Show loading indicator if provided
            if (options.loadingCallback) {
                options.loadingCallback(true);
            }

            // Get current position
            const position = await this.getCurrentPosition();

            // Send location to server
            const response = await this.sendLocationToServer(taskId, action, position);

            // Show success message if provided
            if (options.successCallback) {
                options.successCallback(response);
            }

            return response;

        } catch (error) {
            console.error('Location capture error:', error);
            
            // Show error message if provided
            if (options.errorCallback) {
                options.errorCallback(error);
            } else {
                // Default error handling
                this.showLocationError(error.message);
            }

            throw error;
        } finally {
            // Hide loading indicator if provided
            if (options.loadingCallback) {
                options.loadingCallback(false);
            }
        }
    }

    /**
     * Send location data to server
     */
    async sendLocationToServer(taskId, action, position) {
        const response = await fetch('/master-panel/tasks/location/store', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': this.csrfToken,
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                task_id: taskId,
                latitude: position.latitude,
                longitude: position.longitude,
                action: action
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        
        if (data.error) {
            throw new Error(data.message || 'Failed to save location');
        }

        return data;
    }

    /**
     * Show location permission request dialog
     */
    showLocationPermissionDialog(callback) {
        const modal = `
            <div class="modal fade" id="locationPermissionModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bx bx-map me-2"></i>
                                Location Access Required
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="text-center mb-3">
                                <i class="bx bx-current-location text-primary" style="font-size: 3rem;"></i>
                            </div>
                            <p class="text-center">
                                This application needs access to your location to track task activities. 
                                Your location data helps with work reporting and is only visible to administrators.
                            </p>
                            <div class="alert alert-info">
                                <i class="bx bx-info-circle me-2"></i>
                                You can disable location tracking anytime in your preferences.
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                Cancel
                            </button>
                            <button type="button" class="btn btn-primary" id="allowLocationBtn">
                                Allow Location Access
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('locationPermissionModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modal);

        // Show modal
        const modalElement = new bootstrap.Modal(document.getElementById('locationPermissionModal'));
        modalElement.show();

        // Handle allow button click
        document.getElementById('allowLocationBtn').addEventListener('click', () => {
            modalElement.hide();
            if (callback) callback();
        });
    }

    /**
     * Show location error message
     */
    showLocationError(message) {
        if (typeof toastr !== 'undefined') {
            toastr.error(message, 'Location Error');
        } else {
            alert('Location Error: ' + message);
        }
    }

    /**
     * Show location success message
     */
    showLocationSuccess(message) {
        if (typeof toastr !== 'undefined') {
            toastr.success(message || 'Location captured successfully', 'Location Saved');
        }
    }

    /**
     * Capture location for task creation
     */
    async captureTaskCreationLocation(taskId) {
        return this.captureLocation(taskId, 'task_created', {
            successCallback: (response) => {
                if (!response.location_disabled) {
                    this.showLocationSuccess('Task location captured');
                }
            },
            errorCallback: (error) => {
                console.warn('Task creation location capture failed:', error.message);
                // Don't show error for task creation as it's not critical
            }
        });
    }

    /**
     * Capture location for task check-in
     */
    async captureCheckInLocation(taskId) {
        return this.captureLocation(taskId, 'checkin', {
            successCallback: (response) => {
                if (!response.location_disabled) {
                    this.showLocationSuccess('Check-in location captured');
                }
            },
            errorCallback: (error) => {
                this.showLocationError('Failed to capture check-in location: ' + error.message);
            }
        });
    }

    /**
     * Capture location for task check-out
     */
    async captureCheckOutLocation(taskId) {
        return this.captureLocation(taskId, 'checkout', {
            successCallback: (response) => {
                if (!response.location_disabled) {
                    this.showLocationSuccess('Check-out location captured');
                }
            },
            errorCallback: (error) => {
                this.showLocationError('Failed to capture check-out location: ' + error.message);
            }
        });
    }

    /**
     * Test location access
     */
    async testLocationAccess() {
        try {
            const position = await this.getCurrentPosition();
            this.showLocationSuccess('Location access is working properly');
            return position;
        } catch (error) {
            this.showLocationError('Location test failed: ' + error.message);
            throw error;
        }
    }
}

// Create global instance
window.locationTracker = new LocationTracker();

// Helper functions for backward compatibility
window.captureTaskLocation = function(taskId, action) {
    return window.locationTracker.captureLocation(taskId, action);
};

window.captureTaskCreationLocation = function(taskId) {
    return window.locationTracker.captureTaskCreationLocation(taskId);
};

window.captureCheckInLocation = function(taskId) {
    return window.locationTracker.captureCheckInLocation(taskId);
};

window.captureCheckOutLocation = function(taskId) {
    return window.locationTracker.captureCheckOutLocation(taskId);
};

// Global form submission handler for location tracking integration
$(document).ready(function() {
    // Handle form submissions with location tracking
    $(document).on('submit', '.form-submit-event', function(e) {
        e.preventDefault();

        const form = $(this);
        const actionUrl = form.attr('action');
        const formData = new FormData(this);
        const submitBtn = form.find('button[type="submit"]');
        const originalBtnText = submitBtn.html();

        // Disable submit button
        submitBtn.prop('disabled', true).html('Please wait...');

        // Submit form via AJAX
        $.ajax({
            url: actionUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                console.log('Form submission response:', response); // Debug log

                if (response.error === false) {
                    // Handle location capture for task creation
                    if (response.capture_location && response.id) {
                        if (actionUrl.includes('/tasks/store')) {
                            // Task creation - capture location
                            window.locationTracker.captureTaskCreationLocation(response.id)
                                .catch(error => console.warn('Location capture failed:', error));
                        }
                    }

                    // Show success message
                    if (typeof toastr !== 'undefined') {
                        toastr.success(response.message);
                    }

                    // Handle redirect
                    if (response.redirect_url || form.find('input[name="redirect_url"]').val()) {
                        const redirectUrl = response.redirect_url || form.find('input[name="redirect_url"]').val();
                        console.log('Redirecting to:', redirectUrl); // Debug log
                        setTimeout(() => {
                            try {
                                window.location.href = redirectUrl;
                            } catch (error) {
                                console.error('Redirect failed:', error);
                                // Fallback to dashboard if redirect fails
                                window.location.href = '/master-panel/home';
                            }
                        }, 1000);
                    } else {
                        // Reset form if no redirect
                        form[0].reset();
                    }
                } else {
                    // Show error message
                    if (typeof toastr !== 'undefined') {
                        toastr.error(response.message);
                    }
                }
            },
            error: function(xhr) {
                let errorMessage = 'An error occurred. Please try again.';

                if (xhr.responseJSON) {
                    if (xhr.responseJSON.errors) {
                        // Validation errors
                        $.each(xhr.responseJSON.errors, function(key, value) {
                            if (typeof toastr !== 'undefined') {
                                toastr.error(value[0]);
                            }
                        });
                        return;
                    } else if (xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                }

                if (typeof toastr !== 'undefined') {
                    toastr.error(errorMessage);
                }
            },
            complete: function() {
                // Re-enable submit button
                submitBtn.prop('disabled', false).html(originalBtnText);
            }
        });
    });
});
